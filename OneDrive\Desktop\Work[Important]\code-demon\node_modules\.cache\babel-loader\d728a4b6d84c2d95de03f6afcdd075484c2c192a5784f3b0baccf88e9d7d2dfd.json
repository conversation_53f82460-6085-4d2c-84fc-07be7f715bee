{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\code-demon\\\\src\\\\components\\\\welcome.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Welcome() {\n  _s();\n  useEffect(() => {\n    const text = \"CODE DEMON\";\n    const typingElement = document.getElementById('typing-text');\n    const welcomeScreen = document.getElementById('welcome-screen');\n    if (!typingElement || !welcomeScreen) return;\n    typingElement.classList.add('typing-animation');\n    typingElement.textContent = text;\n    setTimeout(() => {\n      typingElement.classList.remove('typing-animation');\n      typingElement.style.borderRight = 'none';\n      setTimeout(() => {\n        typingElement.classList.add('zoom-fade-animation');\n        setTimeout(() => {\n          welcomeScreen.style.display = 'none';\n        }, 1500);\n      }, 500);\n    }, 1500);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-black h-screen flex items-center justify-center overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"welcome-screen\",\n      className: \"fixed inset-0 bg-black z-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"typing-container text-center w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          id: \"typing-text\",\n          className: \"typing-text text-white text-4xl md:text-6xl mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n}\n_s(Welcome, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Welcome;\nvar _c;\n$RefreshReg$(_c, \"Welcome\");", "map": {"version": 3, "names": ["useEffect", "jsxDEV", "_jsxDEV", "Welcome", "_s", "text", "typingElement", "document", "getElementById", "welcomeScreen", "classList", "add", "textContent", "setTimeout", "remove", "style", "borderRight", "display", "className", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Work[Important]/code-demon/src/components/welcome.jsx"], "sourcesContent": ["\r\n\r\nimport { useEffect } from 'react';\r\nexport default function Welcome() {\r\n  useEffect(() => {\r\n    const text = \"CODE DEMON\";\r\n    const typingElement = document.getElementById('typing-text');\r\n    const welcomeScreen = document.getElementById('welcome-screen');\r\n    if (!typingElement || !welcomeScreen) return;\r\n    typingElement.classList.add('typing-animation');\r\n    typingElement.textContent = text;\r\n    setTimeout(() => {\r\n      typingElement.classList.remove('typing-animation');\r\n      typingElement.style.borderRight = 'none';\r\n      setTimeout(() => {\r\n        typingElement.classList.add('zoom-fade-animation');\r\n        setTimeout(() => {\r\n          welcomeScreen.style.display = 'none';\r\n        }, 1500);\r\n      }, 500);\r\n    }, 1500);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"bg-black h-screen flex items-center justify-center overflow-hidden\">\r\n      <div id=\"welcome-screen\" className=\"fixed inset-0 bg-black z-50 flex items-center justify-center\">\r\n        <div className=\"typing-container text-center w-full\">\r\n          <h1 id=\"typing-text\" className=\"typing-text text-white text-4xl md:text-6xl mx-auto\"></h1>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAEA,SAASA,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAClC,eAAe,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EAChCJ,SAAS,CAAC,MAAM;IACd,MAAMK,IAAI,GAAG,YAAY;IACzB,MAAMC,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC5D,MAAMC,aAAa,GAAGF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAC/D,IAAI,CAACF,aAAa,IAAI,CAACG,aAAa,EAAE;IACtCH,aAAa,CAACI,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/CL,aAAa,CAACM,WAAW,GAAGP,IAAI;IAChCQ,UAAU,CAAC,MAAM;MACfP,aAAa,CAACI,SAAS,CAACI,MAAM,CAAC,kBAAkB,CAAC;MAClDR,aAAa,CAACS,KAAK,CAACC,WAAW,GAAG,MAAM;MACxCH,UAAU,CAAC,MAAM;QACfP,aAAa,CAACI,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClDE,UAAU,CAAC,MAAM;UACfJ,aAAa,CAACM,KAAK,CAACE,OAAO,GAAG,MAAM;QACtC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA;IAAKgB,SAAS,EAAC,oEAAoE;IAAAC,QAAA,eACjFjB,OAAA;MAAKkB,EAAE,EAAC,gBAAgB;MAACF,SAAS,EAAC,8DAA8D;MAAAC,QAAA,eAC/FjB,OAAA;QAAKgB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDjB,OAAA;UAAIkB,EAAE,EAAC,aAAa;UAACF,SAAS,EAAC;QAAqD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpB,EAAA,CA7BuBD,OAAO;AAAAsB,EAAA,GAAPtB,OAAO;AAAA,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}