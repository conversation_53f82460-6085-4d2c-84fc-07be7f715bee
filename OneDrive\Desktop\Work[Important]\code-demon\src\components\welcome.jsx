import { useEffect } from 'react';
export default function Welcome({ onComplete }) {
  useEffect(() => {
    const text = "CODE DEMON";
    const typingElement = document.getElementById('typing-text');
    const welcomeScreen = document.getElementById('welcome-screen');
    if (!typingElement || !welcomeScreen) return;
    typingElement.classList.add('typing-animation');
    typingElement.textContent = text;
    setTimeout(() => {
      typingElement.classList.remove('typing-animation');
      typingElement.style.borderRight = 'none';
      setTimeout(() => {
        typingElement.classList.add('zoom-fade-animation');
        setTimeout(() => {
          welcomeScreen.style.display = 'none';
          if (onComplete) onComplete();
        }, 1500);
      }, 500);
    }, 1500);
  }, []);

  return (

    <div className="bg-black h-screen flex items-center justify-center overflow-hidden">
      <div id="welcome-screen" className="fixed inset-0 bg-black z-50 flex items-center justify-center">
        <div className="typing-container text-center w-full">
          <h1 id="typing-text" className="typing-text text-white text-4xl md:text-6xl mx-auto"></h1>
        </div>
      </div>
    </div>
  );
}
