@import url('https://fonts.googleapis.com/css2?family=Bungee&display=swap');
        /* Custom styles for the typing effect */
        body {
            background-color: #000000;
            overflow: hidden;
        }
        .typing-container {
            position: relative;
            font-family: 'Bungee', cursive;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .typing-text {
            border-right: 0.15em solid #ffffff;
            white-space: nowrap;
            margin-top: 20rem;
            overflow: hidden;
            display: inline-block;
            font-size: 5rem;
            text-align: center;
            color: white;
        }
        
        /* Animation for the typing effect */
        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }
        
        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: #ffffff }
        }
        
        .typing-animation {
            animation: 
                typing 1.5s steps(9, end),
                blink-caret 0.75s step-end infinite;
        }
        
        /* Zoom and fade animation */
        @keyframes zoom-fade {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(3); opacity: 0; }
        }
        
        .zoom-fade-animation {
            animation: zoom-fade 2s ease-out forwards;
        }

/*==============home page====================================================================================================*/

.home-text{
    color: white;
    font-size: 5rem;
}

/* Fade-in animation for home component */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 1s ease-out forwards;
}

.home-container {
    background-color: #000000;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 3rem;
}

/*==============Navigation====================================================================================================*/

.navigation-container {
    position: fixed;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
}

.nav-pill {
    position: relative;
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.sub-pill {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: calc(20% - 0.5rem);
    height: calc(100% - 1rem);
    background: white;
    border-radius: 40px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1;
}

.nav-item {
    position: relative;
    z-index: 2;
    background: transparent;
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
    white-space: nowrap;
    flex: 1;
    text-align: center;
}

.nav-item:hover {
    color: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
}

.nav-item.active {
    color: black;
}

.nav-item.active:hover {
    color: rgba(0, 0, 0, 0.8);
}