@import url('https://fonts.googleapis.com/css2?family=Bungee&display=swap');
        /* Custom styles for the typing effect */
        .typing-container {
            position: relative;
            font-family: 'Bungee', cursive;
        }
        
        .typing-text {
            border-right: 0.15em solid #fff;
            white-space: nowrap;
            overflow: hidden;
            display: inline-block;
        }
        
        /* Animation for the typing effect */
        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }
        
        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: #fff }
        }
        
        .typing-animation {
            animation: 
                typing 1.5s steps(9, end),
                blink-caret 0.75s step-end infinite;
        }
        
        /* Zoom and fade animation */
        @keyframes zoom-fade {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(3); opacity: 0; }
        }
        
        .zoom-fade-animation {
            animation: zoom-fade 2s ease-out forwards;
        }