[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\components\\welcome.jsx": "4"}, {"size": 537, "mtime": 1751899020916, "results": "5", "hashOfConfig": "6"}, {"size": 171, "mtime": 1751898827144, "results": "7", "hashOfConfig": "6"}, {"size": 362, "mtime": 1751897764854, "results": "8", "hashOfConfig": "6"}, {"size": 1194, "mtime": 1751899508252, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7c5yj", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\App.js", ["22"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Work[Important]\\code-demon\\src\\components\\welcome.jsx", ["23"], [], {"ruleId": "24", "severity": 1, "message": "25", "line": 1, "column": 8, "nodeType": "26", "messageId": "27", "endLine": 1, "endColumn": 12}, {"ruleId": "28", "severity": 1, "message": "29", "line": 28, "column": 11, "nodeType": "30", "endLine": 28, "endColumn": 96}, "no-unused-vars", "'logo' is defined but never used.", "Identifier", "unusedVar", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement"]