import { useState } from 'react';

export default function Navigation() {
  const [activeIndex, setActiveIndex] = useState(0);

  const navItems = [
    { name: 'Home', id: 'home' },
    { name: 'About', id: 'about' },
    { name: 'Projects', id: 'projects' },
    { name: 'Skills', id: 'skills' },
    { name: 'Contact', id: 'contact' }
  ];

  const handleNavClick = (index) => {
    setActiveIndex(index);
  };

  return (
    <nav className="navigation-container">
      <div className="nav-pill">
        {/* Sub-pill that travels */}
        <div
          className="sub-pill"
          style={{
            transform: `translateX(${activeIndex * 100}%)`,
          }}
        />

        {/* Navigation items */}
        {navItems.map((item, index) => (
          <button
            key={item.id}
            className={`nav-item ${activeIndex === index ? 'active' : ''}`}
            onClick={() => handleNavClick(index)}
          >
            {item.name}
          </button>
        ))}
      </div>
    </nav>
  );
}